-- Migration script to fix authentication issues
-- Run this in your Supabase SQL editor

-- First, let's add the email column to the profiles table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'profiles'
        AND column_name = 'email'
    ) THEN
        ALTER TABLE public.profiles ADD COLUMN email TEXT;
    END IF;
END $$;

-- Update existing profiles to have email from auth.users
UPDATE public.profiles
SET email = auth_users.email
FROM auth.users AS auth_users
WHERE public.profiles.id = auth_users.id
AND public.profiles.email IS NULL;

-- Now make email NOT NULL and UNIQUE
ALTER TABLE public.profiles ALTER COLUMN email SET NOT NULL;
ALTER TABLE public.profiles ADD CONSTRAINT profiles_email_unique UNIQUE (email);

-- Update the trigger function to include email
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, username, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
        NEW.raw_user_meta_data->>'avatar_url'
    )
    ON CONFLICT (id) DO NOTHING;

    INSERT INTO public.wallets (user_id, coin_balance)
    VALUES (NEW.id, 0)
    ON CONFLICT (user_id) DO NOTHING;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert some sample products for testing
INSERT INTO public.products (name, description, price, image_url) VALUES
('Robot Upgrade Pack', 'Enhance your robot with advanced capabilities', 100, '/images/robot_upgrade.png'),
('Voice Module', 'Add voice recognition to your robot', 50, '/images/voice_module.png'),
('Premium Skin', 'Customize your robot appearance', 25, '/images/premium_skin.png')
ON CONFLICT DO NOTHING;

-- Auto-confirm existing users (if any exist and are not confirmed)
UPDATE auth.users
SET email_confirmed_at = NOW(),
    confirmed_at = NOW()
WHERE email_confirmed_at IS NULL;
