import React from 'react';
import { useWallet } from '../contexts/WalletContext';
import RobotCanvas from '../components/robot/RobotCanvas';
import ChatInterface from '../components/chat/ChatInterface';
import VoiceInput from '../components/voice/VoiceInput';
import AuthDebug from '../components/auth/AuthDebug';

const Dashboard: React.FC = () => {
  const { coinBalance, isLoading } = useWallet();

  const handleVoiceTranscript = (text: string) => {
    // This would be integrated with the chat interface
    console.log('Voice transcript:', text);
    // In a real implementation, we would send this to the chat interface
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Temporary debug component */}
      <AuthDebug />

      <div className="flex flex-col md:flex-row gap-8">
        {/* Left column - Robot and controls */}
        <div className="w-full md:w-1/2 flex flex-col items-center">
          <div className="bg-white rounded-lg shadow-md p-6 w-full">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Your Daswos Robot</h2>

            <div className="flex justify-center mb-6">
              <div className="w-full max-w-md h-80 bg-gray-50 rounded-lg overflow-hidden">
                <RobotCanvas width={400} height={320} />
              </div>
            </div>

            <div className="flex justify-between items-center">
              <div className="bg-blue-100 px-4 py-2 rounded-md">
                <span className="font-bold text-blue-800">
                  {isLoading ? 'Loading...' : `${coinBalance} Coins`}
                </span>
              </div>

              <VoiceInput onTranscript={handleVoiceTranscript} />
            </div>
          </div>
        </div>

        {/* Right column - Chat interface */}
        <div className="w-full md:w-1/2">
          <div className="bg-white rounded-lg shadow-md h-full">
            <ChatInterface />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
