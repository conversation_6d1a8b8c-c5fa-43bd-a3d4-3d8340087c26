import { createClient } from '@supabase/supabase-js';

// Using environment variables or fallback to provided credentials
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://frnbmbozbuudrqotcvfh.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZybmJtYm96YnV1ZHJxb3RjdmZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1MzI0MDgsImV4cCI6MjA2NDEwODQwOH0.YourUpdatedAnonKeyHere';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Authentication helpers
export const signUp = async (email: string, password: string, metadata?: { username?: string }) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: metadata || {},
      emailRedirectTo: undefined // Disable email confirmation
    }
  });
  return { data, error };
};

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

export const getCurrentUser = async () => {
  const { data, error } = await supabase.auth.getUser();
  return { user: data.user, error };
};

export const getSession = async () => {
  const { data, error } = await supabase.auth.getSession();
  return { session: data.session, error };
};

// User profile helpers
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  return { profile: data, error };
};

export const getUserProfileByEmail = async (email: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('email', email)
    .single();
  return { profile: data, error };
};

export const updateUserProfile = async (userId: string, updates: any) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId);
  return { data, error };
};

// Daswos coin wallet helpers
export const getUserCoins = async (userId: string) => {
  const { data, error } = await supabase
    .from('wallets')
    .select('coin_balance')
    .eq('user_id', userId)
    .single();
  return { balance: data?.coin_balance, error };
};

export const updateUserCoins = async (userId: string, newBalance: number) => {
  const { data, error } = await supabase
    .from('wallets')
    .update({ coin_balance: newBalance })
    .eq('user_id', userId);
  return { data, error };
};

// Transaction helpers
export const createTransaction = async (transaction: any) => {
  const { data, error } = await supabase
    .from('transactions')
    .insert([transaction]);
  return { data, error };
};

export const getUserTransactions = async (userId: string) => {
  const { data, error } = await supabase
    .from('transactions')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  return { transactions: data, error };
};

// Product helpers
export const getProducts = async () => {
  const { data, error } = await supabase
    .from('products')
    .select('*');
  return { products: data, error };
};

export const getProductById = async (productId: string) => {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('id', productId)
    .single();
  return { product: data, error };
};
