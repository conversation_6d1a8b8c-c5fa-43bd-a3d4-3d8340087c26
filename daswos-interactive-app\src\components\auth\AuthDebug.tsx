import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { getUserProfile, getUserProfileByEmail } from '../../services/supabase';

const AuthDebug: React.FC = () => {
  const { user, session } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const checkUserProfile = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // Check profile by user ID
      const { profile: profileById, error: errorById } = await getUserProfile(user.id);
      
      // Check profile by email
      const { profile: profileByEmail, error: errorByEmail } = await getUserProfileByEmail(user.email || '');
      
      setDebugInfo({
        user: {
          id: user.id,
          email: user.email,
          user_metadata: user.user_metadata,
          raw_user_meta_data: user.raw_user_meta_data,
        },
        session: session ? {
          access_token: session.access_token ? 'Present' : 'Missing',
          user_id: session.user?.id,
        } : null,
        profileById: {
          data: profileById,
          error: errorById?.message,
        },
        profileByEmail: {
          data: profileByEmail,
          error: errorByEmail?.message,
        }
      });
    } catch (err) {
      console.error('Debug error:', err);
      setDebugInfo({ error: err });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
        <p>No user logged in</p>
      </div>
    );
  }

  return (
    <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
      <h3 className="font-bold mb-2">Auth Debug Info</h3>
      <p><strong>User ID:</strong> {user.id}</p>
      <p><strong>Email:</strong> {user.email}</p>
      
      <button
        onClick={checkUserProfile}
        disabled={loading}
        className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 disabled:opacity-50"
      >
        {loading ? 'Checking...' : 'Check Profile'}
      </button>
      
      {debugInfo && (
        <div className="mt-4">
          <h4 className="font-semibold">Debug Results:</h4>
          <pre className="text-xs bg-white p-2 rounded mt-2 overflow-auto max-h-64">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default AuthDebug;
